<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // ثبت Blade directive برای تاریخ فارسی
        Blade::directive('persianDate', function ($expression) {
            return "<?php echo \App\Helpers\PersianDateHelper::toPersianForArticle($expression); ?>";
        });

        Blade::directive('persianDateTime', function ($expression) {
            return "<?php echo \App\Helpers\PersianDateHelper::toPersianDateTime($expression); ?>";
        });

        Blade::directive('persianDateShort', function ($expression) {
            return "<?php echo \App\Helpers\PersianDateHelper::toPersianShort($expression); ?>";
        });

        Blade::directive('persianDateFull', function ($expression) {
            return "<?php echo \App\Helpers\PersianDateHelper::toPersianFull($expression); ?>";
        });

        Blade::directive('timeAgo', function ($expression) {
            return "<?php echo \App\Helpers\PersianDateHelper::timeAgo($expression); ?>";
        });
    }
}
