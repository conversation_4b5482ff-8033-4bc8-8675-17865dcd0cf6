<?php

namespace App\Helpers;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Verta\Verta;
use Carbon\Carbon;

class PersianDateHelper
{
    /**
     * تبدیل تاریخ میلادی به شمسی
     *
     * @param string|Carbon $date
     * @param string $format
     * @return string
     */
    public static function toPersian($date, $format = 'Y/m/d')
    {
        if (!$date) {
            return '';
        }

        // اگر تاریخ string است، آن را به Carbon تبدیل کن
        if (is_string($date)) {
            $date = Carbon::parse($date);
        }

        // تبدیل به Verta
        $verta = Verta::instance($date);

        return $verta->format($format);
    }

    /**
     * تبدیل تاریخ میلادی به شمسی با زمان
     *
     * @param string|Carbon $date
     * @return string
     */
    public static function toPersianDateTime($date)
    {
        return self::toPersian($date, 'Y/m/d H:i');
    }

    /**
     * تبدیل تاریخ میلادی به شمسی با فرمت کامل
     *
     * @param string|Carbon $date
     * @return string
     */
    public static function toPersianFull($date)
    {
        if (!$date) {
            return '';
        }

        if (is_string($date)) {
            $date = Carbon::parse($date);
        }

        $verta = Verta::instance($date);

        return $verta->format('l، j F Y');
    }

    /**
     * تبدیل تاریخ میلادی به شمسی برای نمایش در مقالات و اخبار
     *
     * @param string|Carbon $date
     * @return string
     */
    public static function toPersianForArticle($date)
    {
        if (!$date) {
            return '';
        }

        if (is_string($date)) {
            $date = Carbon::parse($date);
        }

        $verta = Verta::instance($date);

        return $verta->format('j F Y');
    }

    /**
     * تبدیل تاریخ میلادی به شمسی برای نمودارها
     *
     * @param string|Carbon $date
     * @return string
     */
    public static function toPersianForChart($date)
    {
        if (!$date) {
            return '';
        }

        if (is_string($date)) {
            $date = Carbon::parse($date);
        }

        $verta = Verta::instance($date);

        return $verta->format('j M');
    }

    /**
     * تبدیل تاریخ میلادی به شمسی کوتاه
     *
     * @param string|Carbon $date
     * @return string
     */
    public static function toPersianShort($date)
    {
        return self::toPersian($date, 'Y/m/d');
    }

    /**
     * محاسبه زمان گذشته به فارسی (مثل "2 ساعت پیش")
     *
     * @param string|Carbon $date
     * @return string
     */
    public static function timeAgo($date)
    {
        if (!$date) {
            return '';
        }

        if (is_string($date)) {
            $date = Carbon::parse($date);
        }

        $now = Carbon::now();
        $diff = $date->diffInSeconds($now);

        if ($diff < 60) {
            return 'همین الان';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return $minutes . ' دقیقه پیش';
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return $hours . ' ساعت پیش';
        } elseif ($diff < 2592000) {
            $days = floor($diff / 86400);
            return $days . ' روز پیش';
        } else {
            return self::toPersianForArticle($date);
        }
    }

    /**
     * دریافت نام روز هفته به فارسی
     *
     * @param string|Carbon $date
     * @return string
     */
    public static function getDayName($date)
    {
        if (!$date) {
            return '';
        }

        if (is_string($date)) {
            $date = Carbon::parse($date);
        }

        $verta = Verta::instance($date);

        return $verta->format('l');
    }

    /**
     * دریافت نام ماه به فارسی
     *
     * @param string|Carbon $date
     * @return string
     */
    public static function getMonthName($date)
    {
        if (!$date) {
            return '';
        }

        if (is_string($date)) {
            $date = Carbon::parse($date);
        }

        $verta = Verta::instance($date);

        return $verta->format('F');
    }
}
