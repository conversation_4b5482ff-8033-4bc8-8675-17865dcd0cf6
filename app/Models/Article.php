<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Carbon\Carbon;

class Article extends Model
{
    use HasFactory;

    protected $fillable = [
        'title_fa',
        'title_en',
        'slug',
        'summary_fa',
        'summary_en',
        'content_fa',
        'content_en',
        'featured_image',
        'gallery',
        'is_published',
        'is_featured',
        'published_at',
        'author_id',
        'views_count',
        'meta_title_fa',
        'meta_title_en',
        'meta_description_fa',
        'meta_description_en',
        'meta_keywords',
        'sort_order'
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'is_featured' => 'boolean',
        'published_at' => 'datetime',
        'gallery' => 'array',
        'meta_keywords' => 'array',
        'views_count' => 'integer',
        'sort_order' => 'integer'
    ];

    // روابط
    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'article_categories');
    }

    public function views()
    {
        return $this->hasMany(ArticleView::class);
    }

    // Accessors
    public function getTitleAttribute()
    {
        return app()->getLocale() == 'fa' ? $this->title_fa : ($this->title_en ?? $this->title_fa);
    }

    public function getSummaryAttribute()
    {
        return app()->getLocale() == 'fa' ? $this->summary_fa : ($this->summary_en ?? $this->summary_fa);
    }

    public function getContentAttribute()
    {
        return app()->getLocale() == 'fa' ? $this->content_fa : ($this->content_en ?? $this->content_fa);
    }

    public function getMetaTitleAttribute()
    {
        return app()->getLocale() == 'fa' ? $this->meta_title_fa : ($this->meta_title_en ?? $this->meta_title_fa);
    }

    public function getMetaDescriptionAttribute()
    {
        return app()->getLocale() == 'fa' ? $this->meta_description_fa : ($this->meta_description_en ?? $this->meta_description_fa);
    }

    public function getExcerptAttribute($length = 150)
    {
        $content = strip_tags($this->content);
        return Str::limit($content, $length);
    }

    public function getReadingTimeAttribute()
    {
        $wordCount = str_word_count(strip_tags($this->content));
        return ceil($wordCount / 200); // فرض: 200 کلمه در دقیقه
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('is_published', true)
                    ->where('published_at', '<=', now());
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('published_at', 'desc');
    }

    public function scopePopular($query)
    {
        return $query->orderBy('views_count', 'desc');
    }

    // Methods
    public function incrementViews($ipAddress, $userAgent = null, $userId = null)
    {
        // جلوگیری از شمارش مکرر از همان IP در 24 ساعت
        $existingView = $this->views()
            ->where('ip_address', $ipAddress)
            ->where('viewed_at', '>=', now()->subDay())
            ->first();

        if (!$existingView) {
            $this->views()->create([
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'user_id' => $userId,
                'viewed_at' => now()
            ]);

            $this->increment('views_count');
        }
    }

    // Events
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($article) {
            if (empty($article->slug)) {
                $article->slug = Str::slug($article->title_fa);
            }
            if (empty($article->published_at) && $article->is_published) {
                $article->published_at = now();
            }
        });

        static::updating(function ($article) {
            if (empty($article->slug)) {
                $article->slug = Str::slug($article->title_fa);
            }
            if (empty($article->published_at) && $article->is_published) {
                $article->published_at = now();
            }
        });
    }
}
