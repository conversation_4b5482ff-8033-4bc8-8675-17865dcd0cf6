<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_fa',
        'name_en',
        'slug',
        'description_fa',
        'description_en',
        'type',
        'parent_id',
        'sort_order',
        'is_active',
        'meta_title_fa',
        'meta_title_en',
        'meta_description_fa',
        'meta_description_en'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer'
    ];

    // روابط
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id')->orderBy('sort_order');
    }

    public function articles()
    {
        return $this->belongsToMany(Article::class, 'article_categories');
    }

    public function news()
    {
        return $this->belongsToMany(News::class, 'news_categories');
    }

    // Accessors
    public function getNameAttribute()
    {
        return app()->getLocale() == 'fa' ? $this->name_fa : ($this->name_en ?? $this->name_fa);
    }

    public function getDescriptionAttribute()
    {
        return app()->getLocale() == 'fa' ? $this->description_fa : ($this->description_en ?? $this->description_fa);
    }

    public function getMetaTitleAttribute()
    {
        return app()->getLocale() == 'fa' ? $this->meta_title_fa : ($this->meta_title_en ?? $this->meta_title_fa);
    }

    public function getMetaDescriptionAttribute()
    {
        return app()->getLocale() == 'fa' ? $this->meta_description_fa : ($this->meta_description_en ?? $this->meta_description_fa);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForType($query, $type)
    {
        return $query->where(function($q) use ($type) {
            $q->where('type', $type)->orWhere('type', 'both');
        });
    }

    public function scopeParents($query)
    {
        return $query->whereNull('parent_id');
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name_fa');
    }

    // Events
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($category) {
            if (empty($category->slug)) {
                $category->slug = Str::slug($category->name_fa);
            }
        });

        static::updating(function ($category) {
            if (empty($category->slug)) {
                $category->slug = Str::slug($category->name_fa);
            }
        });
    }
}
