<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // بررسی اینکه کاربر لاگین کرده باشد
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'برای دسترسی به پنل مدیریت باید وارد شوید.');
        }

        // در اینجا می‌توانید شرایط بیشتری برای مدیر بودن اضافه کنید
        // مثلاً بررسی نقش کاربر یا ایمیل خاص
        // فعلاً همه کاربران لاگین شده دسترسی دارند

        return $next($request);
    }
}
