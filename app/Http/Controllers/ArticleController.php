<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Article;
use App\Models\Category;

class ArticleController extends Controller
{
    public function index(Request $request)
    {
        $query = Article::with(['author', 'categories'])
            ->published()
            ->recent();

        // فیلتر بر اساس جستجو
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title_fa', 'like', "%{$search}%")
                  ->orWhere('title_en', 'like', "%{$search}%")
                  ->orWhere('content_fa', 'like', "%{$search}%")
                  ->orWhere('content_en', 'like', "%{$search}%");
            });
        }

        $articles = $query->paginate(12);
        $categories = Category::active()
            ->forType('article')
            ->parents()
            ->ordered()
            ->get();

        return view('articles.index', compact('articles', 'categories'));
    }

    public function show($slug)
    {
        $article = Article::with(['author', 'categories'])
            ->where('slug', $slug)
            ->published()
            ->firstOrFail();

        // افزایش تعداد بازدید
        $article->incrementViews(
            request()->ip(),
            request()->userAgent(),
            auth()->id()
        );

        // مقالات مرتبط
        $relatedArticles = Article::published()
            ->whereHas('categories', function($query) use ($article) {
                $query->whereIn('categories.id', $article->categories->pluck('id'));
            })
            ->where('id', '!=', $article->id)
            ->limit(4)
            ->get();

        return view('articles.show', compact('article', 'relatedArticles'));
    }

    public function category($slug)
    {
        $category = Category::where('slug', $slug)
            ->active()
            ->forType('article')
            ->firstOrFail();

        $articles = Article::with(['author', 'categories'])
            ->published()
            ->whereHas('categories', function($query) use ($category) {
                $query->where('categories.id', $category->id);
            })
            ->recent()
            ->paginate(12);

        return view('articles.category', compact('articles', 'category'));
    }
}
