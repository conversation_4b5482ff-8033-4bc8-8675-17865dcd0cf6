<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\News;
use App\Models\Category;

class NewsController extends Controller
{
    public function index(Request $request)
    {
        $query = News::with(['author', 'categories'])
            ->published()
            ->recent();

        // فیلتر بر اساس جستجو
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title_fa', 'like', "%{$search}%")
                  ->orWhere('title_en', 'like', "%{$search}%")
                  ->orWhere('content_fa', 'like', "%{$search}%")
                  ->orWhere('content_en', 'like', "%{$search}%");
            });
        }

        $news = $query->paginate(12);
        $categories = Category::active()
            ->forType('news')
            ->parents()
            ->ordered()
            ->get();

        return view('news.index', compact('news', 'categories'));
    }

    public function show($slug)
    {
        $news = News::with(['author', 'categories'])
            ->where('slug', $slug)
            ->published()
            ->firstOrFail();

        // افزایش تعداد بازدید
        $news->incrementViews(
            request()->ip(),
            request()->userAgent(),
            auth()->id()
        );

        // اخبار مرتبط
        $relatedNews = News::published()
            ->whereHas('categories', function($query) use ($news) {
                $query->whereIn('categories.id', $news->categories->pluck('id'));
            })
            ->where('id', '!=', $news->id)
            ->limit(4)
            ->get();

        return view('news.show', compact('news', 'relatedNews'));
    }

    public function category($slug)
    {
        $category = Category::where('slug', $slug)
            ->active()
            ->forType('news')
            ->firstOrFail();

        $news = News::with(['author', 'categories'])
            ->published()
            ->whereHas('categories', function($query) use ($category) {
                $query->where('categories.id', $category->id);
            })
            ->recent()
            ->paginate(12);

        return view('news.category', compact('news', 'category'));
    }
}
