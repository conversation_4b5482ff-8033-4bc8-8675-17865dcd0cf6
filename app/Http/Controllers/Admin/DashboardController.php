<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Article;
use App\Models\News;
use App\Models\Category;
use App\Models\ArticleView;
use App\Models\NewsView;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index()
    {
        // آمار کلی
        $stats = [
            'total_articles' => Article::count(),
            'published_articles' => Article::published()->count(),
            'total_news' => News::count(),
            'published_news' => News::published()->count(),
            'total_categories' => Category::active()->count(),
            'total_views_today' => $this->getTodayViews(),
            'total_views_month' => $this->getMonthViews(),
        ];

        // آمار بازدید روزانه (30 روز گذشته)
        $dailyViews = $this->getDailyViewsChart();

        // محبوب‌ترین مقالات
        $popularArticles = Article::published()
            ->orderBy('views_count', 'desc')
            ->limit(5)
            ->get();

        // محبوب‌ترین اخبار
        $popularNews = News::published()
            ->orderBy('views_count', 'desc')
            ->limit(5)
            ->get();

        // آخرین مقالات
        $recentArticles = Article::with('author')
            ->latest()
            ->limit(5)
            ->get();

        // آخرین اخبار
        $recentNews = News::with('author')
            ->latest()
            ->limit(5)
            ->get();

        return view('admin.dashboard', compact(
            'stats',
            'dailyViews',
            'popularArticles',
            'popularNews',
            'recentArticles',
            'recentNews'
        ));
    }

    private function getTodayViews()
    {
        $today = Carbon::today();
        $articleViews = ArticleView::whereDate('viewed_at', $today)->count();
        $newsViews = NewsView::whereDate('viewed_at', $today)->count();
        return $articleViews + $newsViews;
    }

    private function getMonthViews()
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        $articleViews = ArticleView::where('viewed_at', '>=', $startOfMonth)->count();
        $newsViews = NewsView::where('viewed_at', '>=', $startOfMonth)->count();
        return $articleViews + $newsViews;
    }

    private function getDailyViewsChart()
    {
        $days = [];
        $articleViews = [];
        $newsViews = [];

        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $days[] = $date->format('M d');

            $articleViewsCount = ArticleView::whereDate('viewed_at', $date)->count();
            $newsViewsCount = NewsView::whereDate('viewed_at', $date)->count();

            $articleViews[] = $articleViewsCount;
            $newsViews[] = $newsViewsCount;
        }

        return [
            'labels' => $days,
            'article_views' => $articleViews,
            'news_views' => $newsViews,
        ];
    }
}
