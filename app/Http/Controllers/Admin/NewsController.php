<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class NewsController extends Controller
{
    public function index(Request $request)
    {
        $query = News::with(['author', 'categories']);

        // فیلتر بر اساس وضعیت انتشار
        if ($request->has('status')) {
            if ($request->status === 'published') {
                $query->published();
            } elseif ($request->status === 'draft') {
                $query->where('is_published', false);
            } elseif ($request->status === 'featured') {
                $query->featured();
            }
        }

        // فیلتر بر اساس دسته‌بندی
        if ($request->has('category') && $request->category) {
            $query->whereHas('categories', function($q) use ($request) {
                $q->where('categories.id', $request->category);
            });
        }

        // جستجو
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title_fa', 'like', "%{$search}%")
                  ->orWhere('title_en', 'like', "%{$search}%");
            });
        }

        $news = $query->latest()->paginate(15);
        $categories = Category::active()->forType('news')->ordered()->get();

        return view('admin.news.index', compact('news', 'categories'));
    }

    public function create()
    {
        $categories = Category::active()->forType('news')->ordered()->get();
        return view('admin.news.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title_fa' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'slug' => 'nullable|string|max:255|unique:news',
            'summary_fa' => 'nullable|string',
            'summary_en' => 'nullable|string',
            'content_fa' => 'required|string',
            'content_en' => 'nullable|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_published' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'published_at' => 'nullable|date',
            'categories' => 'required|array|min:1',
            'categories.*' => 'exists:categories,id',
            'meta_title_fa' => 'nullable|string|max:255',
            'meta_title_en' => 'nullable|string|max:255',
            'meta_description_fa' => 'nullable|string',
            'meta_description_en' => 'nullable|string',
            'meta_keywords' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title_fa']);
        }

        $validated['author_id'] = auth()->id();
        $validated['is_published'] = $request->has('is_published') && $request->input('is_published') == '1';
        $validated['is_featured'] = $request->has('is_featured') && $request->input('is_featured') == '1';

        // آپلود تصویر شاخص
        if ($request->hasFile('featured_image')) {
            $validated['featured_image'] = $request->file('featured_image')->store('news', 'public');
        }

        // آپلود گالری
        if ($request->hasFile('gallery')) {
            $gallery = [];
            foreach ($request->file('gallery') as $file) {
                $gallery[] = $file->store('news/gallery', 'public');
            }
            $validated['gallery'] = $gallery;
        }

        // پردازش کلمات کلیدی
        if ($request->meta_keywords) {
            $validated['meta_keywords'] = array_map('trim', explode(',', $request->meta_keywords));
        }

        $news = News::create($validated);
        $news->categories()->sync($request->categories);

        return redirect()->route('admin.news.index')
            ->with('success', 'خبر با موفقیت ایجاد شد.');
    }

    public function show(News $news)
    {
        $news->load(['author', 'categories', 'views']);
        return view('admin.news.show', compact('news'));
    }

    public function edit(News $news)
    {
        $categories = Category::active()->forType('news')->ordered()->get();
        return view('admin.news.edit', compact('news', 'categories'));
    }

    public function update(Request $request, News $news)
    {
        $validated = $request->validate([
            'title_fa' => 'required|string|max:255',
            'title_en' => 'nullable|string|max:255',
            'slug' => 'nullable|string|max:255|unique:news,slug,' . $news->id,
            'summary_fa' => 'nullable|string',
            'summary_en' => 'nullable|string',
            'content_fa' => 'required|string',
            'content_en' => 'nullable|string',
            'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_published' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'published_at' => 'nullable|date',
            'categories' => 'required|array|min:1',
            'categories.*' => 'exists:categories,id',
            'meta_title_fa' => 'nullable|string|max:255',
            'meta_title_en' => 'nullable|string|max:255',
            'meta_description_fa' => 'nullable|string',
            'meta_description_en' => 'nullable|string',
            'meta_keywords' => 'nullable|string',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['title_fa']);
        }

        $validated['is_published'] = $request->has('is_published') && $request->input('is_published') == '1';
        $validated['is_featured'] = $request->has('is_featured') && $request->input('is_featured') == '1';

        // آپلود تصویر شاخص جدید
        if ($request->hasFile('featured_image')) {
            if ($news->featured_image) {
                Storage::disk('public')->delete($news->featured_image);
            }
            $validated['featured_image'] = $request->file('featured_image')->store('news', 'public');
        }

        // آپلود گالری جدید
        if ($request->hasFile('gallery')) {
            if ($news->gallery) {
                foreach ($news->gallery as $image) {
                    Storage::disk('public')->delete($image);
                }
            }
            $gallery = [];
            foreach ($request->file('gallery') as $file) {
                $gallery[] = $file->store('news/gallery', 'public');
            }
            $validated['gallery'] = $gallery;
        }

        // پردازش کلمات کلیدی
        if ($request->meta_keywords) {
            $validated['meta_keywords'] = array_map('trim', explode(',', $request->meta_keywords));
        }

        $news->update($validated);
        $news->categories()->sync($request->categories);

        return redirect()->route('admin.news.index')
            ->with('success', 'خبر با موفقیت به‌روزرسانی شد.');
    }

    public function destroy(News $news)
    {
        // حذف تصاویر
        if ($news->featured_image) {
            Storage::disk('public')->delete($news->featured_image);
        }
        if ($news->gallery) {
            foreach ($news->gallery as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $news->delete();

        return redirect()->route('admin.news.index')
            ->with('success', 'خبر با موفقیت حذف شد.');
    }

    public function toggleFeatured(News $news)
    {
        $news->update(['is_featured' => !$news->is_featured]);

        $message = $news->is_featured ? 'خبر به عنوان ویژه انتخاب شد.' : 'خبر از حالت ویژه خارج شد.';

        return redirect()->back()->with('success', $message);
    }
}
