/* RTL specific fixes for dropdown menus */
html[dir="rtl"] .dropdown-menu {
    text-align: right !important;
}

html[dir="rtl"] .dropdown-item {
    text-align: right !important;
    direction: rtl !important;
}

/* Additional class for right-aligned text */
.text-right {
    text-align: right !important;
}

/* Fix for Bootstrap dropdown positioning in RTL */
html[dir="rtl"] .dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

/* Fix for dropdown item padding in RTL */
html[dir="rtl"] .dropdown-item {
    padding: 0.25rem 1rem !important;
    display: block !important;
    width: 100% !important;
    clear: both !important;
    font-weight: 400 !important;
    text-align: inherit !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    background-color: transparent !important;
    border: 0 !important;
}

/* Fix for dropdown toggle caret in RTL */
html[dir="rtl"] .dropdown-toggle::after {
    margin-right: 0.255em !important;
    margin-left: 0 !important;
}

/* Override Bootstrap's default text alignment for dropdown items in RTL */
html[dir="rtl"] .dropdown-menu .dropdown-item {
    text-align: right !important;
}

/* Force text alignment for dropdown items with text-right class */
.dropdown-menu .text-right {
    text-align: right !important;
}

/* Simple Slideshow Styles */
.simple-slideshow {
    position: relative;
}

.slide-item {
    transition: opacity 0.5s ease-in-out;
}

.slideshow-nav .btn {
    border: 2px solid rgba(255,255,255,0.8);
    background: rgba(255,255,255,0.1);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.slideshow-nav .btn:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,1);
}

.dot.active {
    background-color: rgba(255,255,255,1) !important;
}


