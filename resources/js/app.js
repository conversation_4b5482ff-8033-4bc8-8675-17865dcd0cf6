import './bootstrap';

// Enhanced Slideshow and Counter Animation
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Featured Slideshow
    initializeFeaturedSlideshow();

    // Initialize AOS-like animations
    initializeAnimations();
    const startCounters = () => {
        const counters = document.querySelectorAll('.counter-value');

        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            const duration = 2000; // 2 seconds
            const startTime = performance.now();
            const startValue = 0;

            function updateCounter(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Easing function for smooth animation
                const easeOutQuad = progress * (2 - progress);
                const currentValue = Math.floor(startValue + (target - startValue) * easeOutQuad);

                counter.textContent = currentValue.toLocaleString();

                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                }
            }

            requestAnimationFrame(updateCounter);
        });
    };

    // Intersection Observer
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                startCounters();
                observer.disconnect(); // Only run once
            }
        });
    }, { threshold: 0.1 });

    // Observe the stats section
    const statsSection = document.querySelector('.stats-section');
    if (statsSection) {
        observer.observe(statsSection);
    }
});

// Featured Slideshow Functions
function initializeFeaturedSlideshow() {
    const carousel = document.getElementById('featuredCarousel');
    if (!carousel) return;

    console.log('Initializing carousel...');

    // Force initialize Bootstrap carousel
    if (typeof bootstrap !== 'undefined') {
        try {
            // Destroy existing instance if any
            const existingInstance = bootstrap.Carousel.getInstance(carousel);
            if (existingInstance) {
                existingInstance.dispose();
            }

            // Create new instance
            const carouselInstance = new bootstrap.Carousel(carousel, {
                interval: 6000,
                ride: 'carousel',
                pause: 'hover',
                wrap: true,
                keyboard: true
            });

            console.log('Carousel instance created:', carouselInstance);
        } catch (error) {
            console.error('Error creating carousel instance:', error);
        }
    }

    // Enhanced carousel controls
    const prevBtn = carousel.querySelector('.carousel-control-prev');
    const nextBtn = carousel.querySelector('.carousel-control-next');
    const indicators = carousel.querySelectorAll('.indicator-btn');

    // Ensure first item is active
    const items = carousel.querySelectorAll('.carousel-item');
    if (items.length > 0) {
        items.forEach((item, index) => {
            item.classList.remove('active');
            if (index === 0) {
                item.classList.add('active');
            }
        });
    }

    // Ensure first indicator is active
    if (indicators.length > 0) {
        indicators.forEach((indicator, index) => {
            indicator.classList.remove('active');
            if (index === 0) {
                indicator.classList.add('active');
            }
        });
    }

    // Add smooth transitions
    carousel.addEventListener('slide.bs.carousel', function (e) {
        console.log('Carousel slide event triggered');
        const activeItem = carousel.querySelector('.carousel-item.active');
        const nextItem = e.relatedTarget;

        // Update indicators
        const targetIndex = Array.from(items).indexOf(nextItem);
        indicators.forEach((indicator, index) => {
            indicator.classList.toggle('active', index === targetIndex);
        });
    });

    // Enhanced indicator functionality
    indicators.forEach((indicator, index) => {
        indicator.addEventListener('click', function() {
            console.log('Indicator clicked:', index);
            if (typeof bootstrap !== 'undefined') {
                const carouselInstance = bootstrap.Carousel.getInstance(carousel);
                if (carouselInstance) {
                    carouselInstance.to(index);
                }
            }
        });
    });

    // Pause on hover
    carousel.addEventListener('mouseenter', function() {
        if (typeof bootstrap !== 'undefined') {
            const carouselInstance = bootstrap.Carousel.getInstance(carousel);
            if (carouselInstance) {
                carouselInstance.pause();
            }
        }
    });

    carousel.addEventListener('mouseleave', function() {
        if (typeof bootstrap !== 'undefined') {
            const carouselInstance = bootstrap.Carousel.getInstance(carousel);
            if (carouselInstance) {
                carouselInstance.cycle();
            }
        }
    });

    // Touch/swipe support for mobile
    let startX = 0;
    let endX = 0;

    carousel.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
    });

    carousel.addEventListener('touchend', function(e) {
        endX = e.changedTouches[0].clientX;
        handleSwipe();
    });

    function handleSwipe() {
        const threshold = 50;
        const diff = startX - endX;

        if (Math.abs(diff) > threshold) {
            if (typeof bootstrap !== 'undefined') {
                const carouselInstance = bootstrap.Carousel.getInstance(carousel);
                if (carouselInstance) {
                    // Check RTL direction for swipe direction
                    const isRTL = document.documentElement.dir === 'rtl';
                    if (diff > 0) {
                        isRTL ? carouselInstance.prev() : carouselInstance.next();
                    } else {
                        isRTL ? carouselInstance.next() : carouselInstance.prev();
                    }
                }
            }
        }
    }
}

// Simple AOS-like animations
function initializeAnimations() {
    const animatedElements = document.querySelectorAll('[data-aos]');

    const animationObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const delay = element.getAttribute('data-aos-delay') || 0;

                setTimeout(() => {
                    element.classList.add('aos-animate');
                }, parseInt(delay));

                animationObserver.unobserve(element);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    animatedElements.forEach(element => {
        animationObserver.observe(element);
    });
}
