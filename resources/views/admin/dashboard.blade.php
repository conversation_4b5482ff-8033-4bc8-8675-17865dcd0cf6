@extends('layouts.admin')

@section('page-title', app()->getLocale() == 'fa' ? 'داشبورد' : 'Dashboard')
@section('page-subtitle', app()->getLocale() == 'fa' ? 'نمای کلی سیستم مدیریت محتوا' : 'Content Management System Overview')

@section('content')
<!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        کل مقالات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_articles'] }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        مقالات منتشر شده
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['published_articles'] }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-check fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        کل اخبار
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_news'] }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-newspaper fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        بازدید امروز
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $stats['total_views_today'] }}</div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-eye fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row">
                <!-- Views Chart -->
                <div class="col-xl-8 col-lg-7">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">آمار بازدید (30 روز گذشته)</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-area">
                                <canvas id="viewsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Popular Content -->
                <div class="col-xl-4 col-lg-5">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">محبوب‌ترین مقالات</h6>
                        </div>
                        <div class="card-body">
                            @foreach($popularArticles as $article)
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="{{ route('admin.articles.show', $article) }}" class="text-decoration-none">
                                                {{ Str::limit($article->title_fa, 40) }}
                                            </a>
                                        </h6>
                                        <small class="text-muted">{{ $article->views_count }} بازدید</small>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Content -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">آخرین مقالات</h6>
                        </div>
                        <div class="card-body">
                            @foreach($recentArticles as $article)
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="{{ route('admin.articles.show', $article) }}" class="text-decoration-none">
                                                {{ Str::limit($article->title_fa, 40) }}
                                            </a>
                                        </h6>
                                        <small class="text-muted">
                                            {{ $article->author->name }} - {{ app()->getLocale() == 'fa' ? \App\Helpers\PersianDateHelper::timeAgo($article->created_at) : $article->created_at->diffForHumans() }}
                                        </small>
                                    </div>
                                    <div>
                                        @if($article->is_published)
                                            <span class="badge bg-success">منتشر شده</span>
                                        @else
                                            <span class="badge bg-warning">پیش‌نویس</span>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">آخرین اخبار</h6>
                        </div>
                        <div class="card-body">
                            @foreach($recentNews as $news)
                                <div class="d-flex align-items-center mb-3">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="{{ route('admin.news.show', $news) }}" class="text-decoration-none">
                                                {{ Str::limit($news->title_fa, 40) }}
                                            </a>
                                        </h6>
                                        <small class="text-muted">
                                            {{ $news->author->name }} - {{ app()->getLocale() == 'fa' ? \App\Helpers\PersianDateHelper::timeAgo($news->created_at) : $news->created_at->diffForHumans() }}
                                        </small>
                                    </div>
                                    <div>
                                        @if($news->is_published)
                                            <span class="badge bg-success">منتشر شده</span>
                                        @else
                                            <span class="badge bg-warning">پیش‌نویس</span>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Views Chart
const ctx = document.getElementById('viewsChart').getContext('2d');
const viewsChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: @json($dailyViews['labels']),
        datasets: [{
            label: 'بازدید مقالات',
            data: @json($dailyViews['article_views']),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }, {
            label: 'بازدید اخبار',
            data: @json($dailyViews['news_views']),
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'آمار بازدید روزانه'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

</style>
@endsection
