@extends('layouts.admin')

@section('page-title', app()->getLocale() == 'fa' ? 'ویرایش مقاله' : 'Edit Article')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div></div>
        <a href="{{ route('admin.articles.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i>{{ app()->getLocale() == 'fa' ? 'بازگشت' : 'Back' }}
        </a>
    </div>

    @if($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form method="POST" action="{{ route('admin.articles.update', $article) }}" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">اطلاعات اصلی</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="title_fa" class="form-label">عنوان فارسی *</label>
                            <input type="text" class="form-control" id="title_fa" name="title_fa"
                                   value="{{ old('title_fa', $article->title_fa) }}" required>
                        </div>

                        <div class="mb-3">
                            <label for="title_en" class="form-label">عنوان انگلیسی</label>
                            <input type="text" class="form-control" id="title_en" name="title_en"
                                   value="{{ old('title_en', $article->title_en) }}">
                        </div>

                        <div class="mb-3">
                            <label for="slug" class="form-label">نامک (Slug)</label>
                            <input type="text" class="form-control" id="slug" name="slug"
                                   value="{{ old('slug', $article->slug) }}" placeholder="به صورت خودکار تولید می‌شود">
                        </div>

                        <div class="mb-3">
                            <label for="summary_fa" class="form-label">خلاصه فارسی</label>
                            <textarea class="form-control" id="summary_fa" name="summary_fa" rows="3">{{ old('summary_fa', $article->summary_fa) }}</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="summary_en" class="form-label">خلاصه انگلیسی</label>
                            <textarea class="form-control" id="summary_en" name="summary_en" rows="3">{{ old('summary_en', $article->summary_en) }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Content -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">محتوا</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="content_fa" class="form-label">محتوای فارسی *</label>
                            <textarea class="form-control ckeditor" id="content_fa" name="content_fa" rows="15">{{ old('content_fa', $article->content_fa) }}</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="content_en" class="form-label">محتوای انگلیسی</label>
                            <textarea class="form-control ckeditor" id="content_en" name="content_en" rows="15">{{ old('content_en', $article->content_en) }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- SEO -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">تنظیمات SEO</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="meta_title_fa" class="form-label">عنوان متا فارسی</label>
                                    <input type="text" class="form-control" id="meta_title_fa" name="meta_title_fa"
                                           value="{{ old('meta_title_fa', $article->meta_title_fa) }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="meta_title_en" class="form-label">عنوان متا انگلیسی</label>
                                    <input type="text" class="form-control" id="meta_title_en" name="meta_title_en"
                                           value="{{ old('meta_title_en', $article->meta_title_en) }}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="meta_description_fa" class="form-label">توضیحات متا فارسی</label>
                                    <textarea class="form-control" id="meta_description_fa" name="meta_description_fa" rows="3">{{ old('meta_description_fa', $article->meta_description_fa) }}</textarea>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="meta_description_en" class="form-label">توضیحات متا انگلیسی</label>
                                    <textarea class="form-control" id="meta_description_en" name="meta_description_en" rows="3">{{ old('meta_description_en', $article->meta_description_en) }}</textarea>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="meta_keywords" class="form-label">کلمات کلیدی</label>
                            <input type="text" class="form-control" id="meta_keywords" name="meta_keywords"
                                   value="{{ old('meta_keywords', is_array($article->meta_keywords) ? implode(', ', $article->meta_keywords) : '') }}"
                                   placeholder="کلمات را با کاما جدا کنید">
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Publish Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">تنظیمات انتشار</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_published" name="is_published" value="1"
                                       {{ old('is_published', $article->is_published) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_published">
                                    منتشر شود
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1"
                                       {{ old('is_featured', $article->is_featured) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_featured">
                                    مقاله ویژه (نمایش در اسلایدشو)
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="published_at" class="form-label">تاریخ انتشار</label>
                            <input type="datetime-local" class="form-control" id="published_at" name="published_at"
                                   value="{{ old('published_at', $article->published_at ? $article->published_at->format('Y-m-d\TH:i') : '') }}">
                        </div>

                        <div class="mb-3">
                            <label for="sort_order" class="form-label">ترتیب نمایش</label>
                            <input type="number" class="form-control" id="sort_order" name="sort_order"
                                   value="{{ old('sort_order', $article->sort_order) }}" min="0">
                        </div>
                    </div>
                </div>

                <!-- Categories -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">دسته‌بندی‌ها *</h5>
                    </div>
                    <div class="card-body">
                        @foreach($categories as $category)
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="category_{{ $category->id }}"
                                       name="categories[]" value="{{ $category->id }}"
                                       {{ in_array($category->id, old('categories', $article->categories->pluck('id')->toArray())) ? 'checked' : '' }}>
                                <label class="form-check-label" for="category_{{ $category->id }}">
                                    {{ $category->name_fa }}
                                </label>
                            </div>
                            @if($category->children->count() > 0)
                                @foreach($category->children as $child)
                                    <div class="form-check mb-2 ms-3">
                                        <input class="form-check-input" type="checkbox" id="category_{{ $child->id }}"
                                               name="categories[]" value="{{ $child->id }}"
                                               {{ in_array($child->id, old('categories', $article->categories->pluck('id')->toArray())) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="category_{{ $child->id }}">
                                            {{ $child->name_fa }}
                                        </label>
                                    </div>
                                @endforeach
                            @endif
                        @endforeach
                    </div>
                </div>

                <!-- Featured Image -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">تصویر شاخص</h5>
                    </div>
                    <div class="card-body">
                        @if($article->featured_image)
                            <div class="mb-3">
                                <img src="{{ asset('storage/' . $article->featured_image) }}"
                                     class="img-fluid rounded"
                                     alt="Current Featured Image"
                                     style="max-height: 200px;">
                                <p class="small text-muted mt-2">تصویر فعلی</p>
                            </div>
                        @endif
                        <div class="mb-3">
                            <input type="file" class="form-control" id="featured_image" name="featured_image"
                                   accept="image/*">
                            <div class="form-text">فرمت‌های مجاز: JPG, PNG, GIF - حداکثر 2MB</div>
                        </div>
                    </div>
                </div>

                <!-- Gallery -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">گالری تصاویر</h5>
                    </div>
                    <div class="card-body">
                        @if($article->gallery && count($article->gallery) > 0)
                            <div class="mb-3">
                                <p class="small text-muted">تصاویر فعلی:</p>
                                <div class="row">
                                    @foreach($article->gallery as $image)
                                    <div class="col-6 mb-2">
                                        <img src="{{ asset('storage/' . $image) }}"
                                             class="img-fluid rounded"
                                             alt="Gallery Image"
                                             style="max-height: 100px;">
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                        <div class="mb-3">
                            <input type="file" class="form-control" id="gallery" name="gallery[]"
                                   accept="image/*" multiple>
                            <div class="form-text">می‌توانید چندین تصویر انتخاب کنید (تصاویر قبلی جایگزین خواهند شد)</div>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="card">
                    <div class="card-body">
                        <button type="submit" class="btn btn-primary w-100 mb-2">
                            <i class="fas fa-save me-1"></i>به‌روزرسانی مقاله
                        </button>
                        <a href="{{ route('admin.articles.show', $article) }}" class="btn btn-outline-info w-100 mb-2">
                            <i class="fas fa-eye me-1"></i>مشاهده مقاله
                        </a>
                        <a href="{{ route('admin.articles.index') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-times me-1"></i>انصراف
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize CKEditor for all textareas with ckeditor class
    document.querySelectorAll('.ckeditor').forEach(function(textarea) {
        ClassicEditor
            .create(textarea, {
                language: 'fa',
                toolbar: {
                    items: [
                        'heading',
                        '|',
                        'bold',
                        'italic',
                        'underline',
                        '|',
                        'link',
                        'bulletedList',
                        'numberedList',
                        '|',
                        'outdent',
                        'indent',
                        '|',
                        'blockQuote',
                        'insertTable',
                        '|',
                        'undo',
                        'redo',
                        '|',
                        'alignment',
                        'fontColor',
                        'fontBackgroundColor',
                        '|',
                        'sourceEditing'
                    ]
                },
                heading: {
                    options: [
                        { model: 'paragraph', title: 'پاراگراف', class: 'ck-heading_paragraph' },
                        { model: 'heading1', view: 'h1', title: 'عنوان 1', class: 'ck-heading_heading1' },
                        { model: 'heading2', view: 'h2', title: 'عنوان 2', class: 'ck-heading_heading2' },
                        { model: 'heading3', view: 'h3', title: 'عنوان 3', class: 'ck-heading_heading3' },
                        { model: 'heading4', view: 'h4', title: 'عنوان 4', class: 'ck-heading_heading4' }
                    ]
                },
                table: {
                    contentToolbar: [
                        'tableColumn',
                        'tableRow',
                        'mergeTableCells'
                    ]
                }
            })
            .catch(error => {
                console.error('CKEditor initialization error:', error);
            });
    });
});
</script>
@endsection
