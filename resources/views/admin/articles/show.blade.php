@extends('layouts.admin')

@section('page-title', app()->getLocale() == 'fa' ? 'نمایش مقاله' : 'View Article')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4>{{ app()->getLocale() == 'fa' ? 'نمایش مقاله' : 'View Article' }}</h4>
                    <div>
                        <a href="{{ route('admin.articles.edit', $article) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-1"></i>{{ app()->getLocale() == 'fa' ? 'ویرایش' : 'Edit' }}
                        </a>
                        <a href="{{ route('admin.articles.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>{{ app()->getLocale() == 'fa' ? 'بازگشت' : 'Back' }}
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8">
                            <!-- Basic Information -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3">{{ app()->getLocale() == 'fa' ? 'اطلاعات اصلی' : 'Basic Information' }}</h5>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'عنوان فارسی:' : 'Persian Title:' }}</strong>
                                        <p class="mb-0">{{ $article->title_fa }}</p>
                                    </div>
                                    @if($article->title_en)
                                    <div class="col-md-6">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'عنوان انگلیسی:' : 'English Title:' }}</strong>
                                        <p class="mb-0">{{ $article->title_en }}</p>
                                    </div>
                                    @endif
                                </div>

                                <div class="mb-3">
                                    <strong>{{ app()->getLocale() == 'fa' ? 'نامک (Slug):' : 'Slug:' }}</strong>
                                    <p class="mb-0"><code>{{ $article->slug }}</code></p>
                                </div>

                                @if($article->summary_fa || $article->summary_en)
                                <div class="row mb-3">
                                    @if($article->summary_fa)
                                    <div class="col-md-6">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'خلاصه فارسی:' : 'Persian Summary:' }}</strong>
                                        <p class="mb-0">{{ $article->summary_fa }}</p>
                                    </div>
                                    @endif
                                    @if($article->summary_en)
                                    <div class="col-md-6">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'خلاصه انگلیسی:' : 'English Summary:' }}</strong>
                                        <p class="mb-0">{{ $article->summary_en }}</p>
                                    </div>
                                    @endif
                                </div>
                                @endif
                            </div>

                            <!-- Content -->
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3">{{ app()->getLocale() == 'fa' ? 'محتوا' : 'Content' }}</h5>
                                
                                <div class="mb-3">
                                    <strong>{{ app()->getLocale() == 'fa' ? 'محتوای فارسی:' : 'Persian Content:' }}</strong>
                                    <div class="border p-3 bg-light mt-2" style="max-height: 300px; overflow-y: auto;">
                                        {!! nl2br(e($article->content_fa)) !!}
                                    </div>
                                </div>

                                @if($article->content_en)
                                <div class="mb-3">
                                    <strong>{{ app()->getLocale() == 'fa' ? 'محتوای انگلیسی:' : 'English Content:' }}</strong>
                                    <div class="border p-3 bg-light mt-2" style="max-height: 300px; overflow-y: auto;">
                                        {!! nl2br(e($article->content_en)) !!}
                                    </div>
                                </div>
                                @endif
                            </div>

                            <!-- SEO Information -->
                            @if($article->meta_title_fa || $article->meta_title_en || $article->meta_description_fa || $article->meta_description_en || $article->meta_keywords)
                            <div class="mb-4">
                                <h5 class="border-bottom pb-2 mb-3">{{ app()->getLocale() == 'fa' ? 'اطلاعات SEO' : 'SEO Information' }}</h5>
                                
                                @if($article->meta_title_fa || $article->meta_title_en)
                                <div class="row mb-3">
                                    @if($article->meta_title_fa)
                                    <div class="col-md-6">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'عنوان متا فارسی:' : 'Persian Meta Title:' }}</strong>
                                        <p class="mb-0">{{ $article->meta_title_fa }}</p>
                                    </div>
                                    @endif
                                    @if($article->meta_title_en)
                                    <div class="col-md-6">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'عنوان متا انگلیسی:' : 'English Meta Title:' }}</strong>
                                        <p class="mb-0">{{ $article->meta_title_en }}</p>
                                    </div>
                                    @endif
                                </div>
                                @endif

                                @if($article->meta_description_fa || $article->meta_description_en)
                                <div class="row mb-3">
                                    @if($article->meta_description_fa)
                                    <div class="col-md-6">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'توضیحات متا فارسی:' : 'Persian Meta Description:' }}</strong>
                                        <p class="mb-0">{{ $article->meta_description_fa }}</p>
                                    </div>
                                    @endif
                                    @if($article->meta_description_en)
                                    <div class="col-md-6">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'توضیحات متا انگلیسی:' : 'English Meta Description:' }}</strong>
                                        <p class="mb-0">{{ $article->meta_description_en }}</p>
                                    </div>
                                    @endif
                                </div>
                                @endif

                                @if($article->meta_keywords)
                                <div class="mb-3">
                                    <strong>{{ app()->getLocale() == 'fa' ? 'کلمات کلیدی:' : 'Keywords:' }}</strong>
                                    <div class="mt-2">
                                        @foreach($article->meta_keywords as $keyword)
                                            <span class="badge bg-secondary me-1">{{ $keyword }}</span>
                                        @endforeach
                                    </div>
                                </div>
                                @endif
                            </div>
                            @endif
                        </div>

                        <div class="col-lg-4">
                            <!-- Status Information -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">{{ app()->getLocale() == 'fa' ? 'وضعیت انتشار' : 'Publication Status' }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'وضعیت:' : 'Status:' }}</strong>
                                        <div class="mt-1">
                                            @if($article->is_published)
                                                <span class="badge bg-success">{{ app()->getLocale() == 'fa' ? 'منتشر شده' : 'Published' }}</span>
                                            @else
                                                <span class="badge bg-warning">{{ app()->getLocale() == 'fa' ? 'پیش‌نویس' : 'Draft' }}</span>
                                            @endif
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'مقاله ویژه:' : 'Featured:' }}</strong>
                                        <div class="mt-1">
                                            @if($article->is_featured)
                                                <span class="badge bg-primary">{{ app()->getLocale() == 'fa' ? 'بله' : 'Yes' }}</span>
                                            @else
                                                <span class="badge bg-secondary">{{ app()->getLocale() == 'fa' ? 'خیر' : 'No' }}</span>
                                            @endif
                                        </div>
                                    </div>

                                    @if($article->published_at)
                                    <div class="mb-3">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'تاریخ انتشار:' : 'Published At:' }}</strong>
                                        <p class="mb-0 small">{{ $article->published_at->format('Y-m-d H:i:s') }}</p>
                                    </div>
                                    @endif

                                    <div class="mb-3">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'تعداد بازدید:' : 'Views Count:' }}</strong>
                                        <p class="mb-0">{{ number_format($article->views_count) }}</p>
                                    </div>

                                    @if($article->sort_order)
                                    <div class="mb-3">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'ترتیب نمایش:' : 'Sort Order:' }}</strong>
                                        <p class="mb-0">{{ $article->sort_order }}</p>
                                    </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Author Information -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">{{ app()->getLocale() == 'fa' ? 'اطلاعات نویسنده' : 'Author Information' }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'نویسنده:' : 'Author:' }}</strong>
                                        <p class="mb-0">{{ $article->author->name }}</p>
                                    </div>
                                    <div class="mb-3">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'تاریخ ایجاد:' : 'Created At:' }}</strong>
                                        <p class="mb-0 small">{{ $article->created_at->format('Y-m-d H:i:s') }}</p>
                                    </div>
                                    <div class="mb-0">
                                        <strong>{{ app()->getLocale() == 'fa' ? 'آخرین به‌روزرسانی:' : 'Updated At:' }}</strong>
                                        <p class="mb-0 small">{{ $article->updated_at->format('Y-m-d H:i:s') }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Categories -->
                            @if($article->categories->count() > 0)
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">{{ app()->getLocale() == 'fa' ? 'دسته‌بندی‌ها' : 'Categories' }}</h6>
                                </div>
                                <div class="card-body">
                                    @foreach($article->categories as $category)
                                        <span class="badge bg-secondary me-1 mb-1">{{ $category->name_fa }}</span>
                                    @endforeach
                                </div>
                            </div>
                            @endif

                            <!-- Featured Image -->
                            @if($article->featured_image)
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">{{ app()->getLocale() == 'fa' ? 'تصویر شاخص' : 'Featured Image' }}</h6>
                                </div>
                                <div class="card-body">
                                    <img src="{{ asset('storage/' . $article->featured_image) }}" 
                                         class="img-fluid rounded" 
                                         alt="{{ $article->title_fa }}">
                                </div>
                            </div>
                            @endif

                            <!-- Gallery -->
                            @if($article->gallery && count($article->gallery) > 0)
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">{{ app()->getLocale() == 'fa' ? 'گالری تصاویر' : 'Image Gallery' }}</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        @foreach($article->gallery as $image)
                                        <div class="col-6 mb-2">
                                            <img src="{{ asset('storage/' . $image) }}" 
                                                 class="img-fluid rounded" 
                                                 alt="Gallery Image">
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
