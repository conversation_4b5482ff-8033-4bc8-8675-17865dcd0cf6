@extends('layouts.admin')

@section('page-title', app()->getLocale() == 'fa' ? 'مدیریت مقالات' : 'Articles Management')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div></div>
        <a href="{{ route('admin.articles.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>{{ app()->getLocale() == 'fa' ? 'مقاله جدید' : 'New Article' }}
        </a>
    </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('admin.articles.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="search" class="form-label">جستجو</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       value="{{ request('search') }}" placeholder="عنوان مقاله...">
                            </div>
                            <div class="col-md-3">
                                <label for="status" class="form-label">وضعیت</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">همه</option>
                                    <option value="published" {{ request('status') === 'published' ? 'selected' : '' }}>منتشر شده</option>
                                    <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>پیش‌نویس</option>
                                    <option value="featured" {{ request('status') === 'featured' ? 'selected' : '' }}>ویژه</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="category" class="form-label">دسته‌بندی</label>
                                <select class="form-select" id="category" name="category">
                                    <option value="">همه دسته‌ها</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ request('category') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name_fa }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-search me-1"></i>جستجو
                                </button>
                                <a href="{{ route('admin.articles.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>پاک کردن
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Articles Table -->
            <div class="card">
                <div class="card-body">
                    @if($articles->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>عنوان</th>
                                        <th>نویسنده</th>
                                        <th>دسته‌بندی</th>
                                        <th>وضعیت</th>
                                        <th>بازدید</th>
                                        <th>تاریخ ایجاد</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($articles as $article)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($article->featured_image)
                                                        <img src="{{ asset('storage/' . $article->featured_image) }}"
                                                             class="me-3 rounded"
                                                             style="width: 50px; height: 40px; object-fit: cover;">
                                                    @endif
                                                    <div>
                                                        <h6 class="mb-0">{{ Str::limit($article->title_fa, 50) }}</h6>
                                                        @if($article->is_featured)
                                                            <span class="badge bg-warning text-dark">ویژه</span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $article->author->name }}</td>
                                            <td>
                                                @foreach($article->categories->take(2) as $category)
                                                    <span class="badge bg-secondary me-1">{{ $category->name_fa }}</span>
                                                @endforeach
                                                @if($article->categories->count() > 2)
                                                    <span class="text-muted">+{{ $article->categories->count() - 2 }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($article->is_published)
                                                    <span class="badge bg-success">منتشر شده</span>
                                                @else
                                                    <span class="badge bg-warning">پیش‌نویس</span>
                                                @endif
                                            </td>
                                            <td>{{ $article->views_count }}</td>
                                            <td>{{ $article->created_at->format('Y/m/d') }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.articles.show', $article) }}"
                                                       class="btn btn-sm btn-outline-info" title="مشاهده">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.articles.edit', $article) }}"
                                                       class="btn btn-sm btn-outline-primary" title="ویرایش">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="POST" action="{{ route('admin.articles.toggle-featured', $article) }}"
                                                          class="d-inline">
                                                        @csrf
                                                        <button type="submit" class="btn btn-sm btn-outline-warning"
                                                                title="{{ $article->is_featured ? 'حذف از ویژه' : 'انتخاب به عنوان ویژه' }}">
                                                            <i class="fas fa-star{{ $article->is_featured ? '' : '-o' }}"></i>
                                                        </button>
                                                    </form>
                                                    <form method="POST" action="{{ route('admin.articles.destroy', $article) }}"
                                                          class="d-inline" onsubmit="return confirm('آیا مطمئن هستید؟')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            {{ $articles->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">هیچ مقاله‌ای یافت نشد</h5>
                            <p class="text-muted">اولین مقاله خود را ایجاد کنید</p>
                            <a href="{{ route('admin.articles.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>مقاله جدید
                            </a>
                        </div>
                    @endif
                </div>
            </div>
</div>
@endsection
