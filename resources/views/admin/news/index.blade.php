@extends('layouts.admin')

@section('page-title', app()->getLocale() == 'fa' ? 'مدیریت اخبار' : 'News Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4>{{ app()->getLocale() == 'fa' ? 'مدیریت اخبار' : 'News Management' }}</h4>
                    <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
                        {{ app()->getLocale() == 'fa' ? 'خبر جدید' : 'New News' }}
                    </a>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{{ app()->getLocale() == 'fa' ? 'شناسه' : 'ID' }}</th>
                                    <th>{{ app()->getLocale() == 'fa' ? 'عنوان' : 'Title' }}</th>
                                    <th>{{ app()->getLocale() == 'fa' ? 'دسته‌بندی' : 'Category' }}</th>
                                    <th>{{ app()->getLocale() == 'fa' ? 'وضعیت' : 'Status' }}</th>
                                    <th>{{ app()->getLocale() == 'fa' ? 'تاریخ ایجاد' : 'Created At' }}</th>
                                    <th>{{ app()->getLocale() == 'fa' ? 'عملیات' : 'Actions' }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($news as $item)
                                    <tr>
                                        <td>{{ $item->id }}</td>
                                        <td>{{ $item->title }}</td>
                                        <td>{{ $item->category->name ?? '-' }}</td>
                                        <td>
                                            <span class="badge bg-{{ $item->is_active ? 'success' : 'secondary' }}">
                                                {{ $item->is_active ? (app()->getLocale() == 'fa' ? 'فعال' : 'Active') : (app()->getLocale() == 'fa' ? 'غیرفعال' : 'Inactive') }}
                                            </span>
                                        </td>
                                        <td>{{ app()->getLocale() == 'fa' ? \App\Helpers\PersianDateHelper::toPersianShort($item->created_at) : $item->created_at->format('Y-m-d') }}</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="{{ route('admin.news.show', $item) }}" class="btn btn-sm btn-info">
                                                    {{ app()->getLocale() == 'fa' ? 'نمایش' : 'View' }}
                                                </a>
                                                <a href="{{ route('admin.news.edit', $item) }}" class="btn btn-sm btn-warning">
                                                    {{ app()->getLocale() == 'fa' ? 'ویرایش' : 'Edit' }}
                                                </a>
                                                <form action="{{ route('admin.news.destroy', $item) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-danger"
                                                            onclick="return confirm('{{ app()->getLocale() == 'fa' ? 'آیا مطمئن هستید؟' : 'Are you sure?' }}')">
                                                        {{ app()->getLocale() == 'fa' ? 'حذف' : 'Delete' }}
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">
                                            {{ app()->getLocale() == 'fa' ? 'هیچ خبری یافت نشد' : 'No news found' }}
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    @if(isset($news) && method_exists($news, 'links'))
                        {{ $news->links() }}
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
