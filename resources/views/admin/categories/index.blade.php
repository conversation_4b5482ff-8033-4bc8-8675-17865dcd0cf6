@extends('layouts.admin')

@section('page-title', app()->getLocale() == 'fa' ? 'مدیریت دسته‌بندی‌ها' : 'Categories Management')

@section('content')
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div></div>
        <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>{{ app()->getLocale() == 'fa' ? 'دسته‌بندی جدید' : 'New Category' }}
        </a>
    </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Categories Table -->
            <div class="card">
                <div class="card-body">
                    @if($categories->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>نام</th>
                                        <th>نوع</th>
                                        <th>والد</th>
                                        <th>ترتیب</th>
                                        <th>وضعیت</th>
                                        <th>تاریخ ایجاد</th>
                                        <th>عملیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($categories as $category)
                                        <tr>
                                            <td>
                                                <div>
                                                    <h6 class="mb-0">{{ $category->name_fa }}</h6>
                                                    @if($category->name_en)
                                                        <small class="text-muted">{{ $category->name_en }}</small>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                @if($category->type === 'article')
                                                    <span class="badge bg-primary">مقاله</span>
                                                @elseif($category->type === 'news')
                                                    <span class="badge bg-info">خبر</span>
                                                @else
                                                    <span class="badge bg-secondary">هر دو</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($category->parent)
                                                    <span class="text-muted">{{ $category->parent->name_fa }}</span>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>
                                            <td>{{ $category->sort_order }}</td>
                                            <td>
                                                @if($category->is_active)
                                                    <span class="badge bg-success">فعال</span>
                                                @else
                                                    <span class="badge bg-danger">غیرفعال</span>
                                                @endif
                                            </td>
                                            <td>{{ $category->created_at->format('Y/m/d') }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.categories.show', $category) }}"
                                                       class="btn btn-sm btn-outline-info" title="مشاهده">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.categories.edit', $category) }}"
                                                       class="btn btn-sm btn-outline-primary" title="ویرایش">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="POST" action="{{ route('admin.categories.destroy', $category) }}"
                                                          class="d-inline" onsubmit="return confirm('آیا مطمئن هستید؟')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" title="حذف">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            {{ $categories->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-folder fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">هیچ دسته‌بندی‌ای یافت نشد</h5>
                            <p class="text-muted">اولین دسته‌بندی خود را ایجاد کنید</p>
                            <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>دسته‌بندی جدید
                            </a>
                        </div>
                    @endif
                </div>
            </div>
</div>
@endsection
