@extends('layouts.admin')

@section('page-title', app()->getLocale() == 'fa' ? 'ایجاد دسته‌بندی جدید' : 'Create New Category')

@section('content')
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="position-sticky pt-3">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                    <span>مدیریت محتوا</span>
                </h6>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-2"></i>داشبورد
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.articles.index') }}">
                            <i class="fas fa-file-alt me-2"></i>مقالات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.news.index') }}">
                            <i class="fas fa-newspaper me-2"></i>اخبار
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ route('admin.categories.index') }}">
                            <i class="fas fa-folder me-2"></i>دسته‌بندی‌ها
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">ایجاد دسته‌بندی جدید</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>بازگشت
                    </a>
                </div>
            </div>

            @if($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="row">
                <div class="col-lg-8">
                    <form method="POST" action="{{ route('admin.categories.store') }}">
                        @csrf

                        <!-- Basic Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">اطلاعات اصلی</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name_fa" class="form-label">نام فارسی *</label>
                                            <input type="text" class="form-control" id="name_fa" name="name_fa"
                                                   value="{{ old('name_fa') }}" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="name_en" class="form-label">نام انگلیسی</label>
                                            <input type="text" class="form-control" id="name_en" name="name_en"
                                                   value="{{ old('name_en') }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="slug" class="form-label">نامک (Slug)</label>
                                    <input type="text" class="form-control" id="slug" name="slug"
                                           value="{{ old('slug') }}" placeholder="به صورت خودکار تولید می‌شود">
                                    <div class="form-text">برای URL استفاده می‌شود. اگر خالی باشد، از نام فارسی تولید می‌شود.</div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="description_fa" class="form-label">توضیحات فارسی</label>
                                            <textarea class="form-control" id="description_fa" name="description_fa" rows="4">{{ old('description_fa') }}</textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="description_en" class="form-label">توضیحات انگلیسی</label>
                                            <textarea class="form-control" id="description_en" name="description_en" rows="4">{{ old('description_en') }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">تنظیمات</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="type" class="form-label">نوع *</label>
                                            <select class="form-select" id="type" name="type" required>
                                                <option value="">انتخاب کنید</option>
                                                <option value="article" {{ old('type') === 'article' ? 'selected' : '' }}>مقاله</option>
                                                <option value="news" {{ old('type') === 'news' ? 'selected' : '' }}>خبر</option>
                                                <option value="both" {{ old('type') === 'both' ? 'selected' : '' }}>هر دو</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="parent_id" class="form-label">دسته‌بندی والد</label>
                                            <select class="form-select" id="parent_id" name="parent_id">
                                                <option value="">بدون والد (دسته اصلی)</option>
                                                @foreach($parentCategories as $parent)
                                                    <option value="{{ $parent->id }}" {{ old('parent_id') == $parent->id ? 'selected' : '' }}>
                                                        {{ $parent->name_fa }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="sort_order" class="form-label">ترتیب نمایش</label>
                                            <input type="number" class="form-control" id="sort_order" name="sort_order"
                                                   value="{{ old('sort_order', 0) }}" min="0">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                               {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            فعال
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- SEO Settings -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">تنظیمات SEO</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="meta_title_fa" class="form-label">عنوان متا فارسی</label>
                                            <input type="text" class="form-control" id="meta_title_fa" name="meta_title_fa"
                                                   value="{{ old('meta_title_fa') }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="meta_title_en" class="form-label">عنوان متا انگلیسی</label>
                                            <input type="text" class="form-control" id="meta_title_en" name="meta_title_en"
                                                   value="{{ old('meta_title_en') }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="meta_description_fa" class="form-label">توضیحات متا فارسی</label>
                                            <textarea class="form-control" id="meta_description_fa" name="meta_description_fa" rows="3">{{ old('meta_description_fa') }}</textarea>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="meta_description_en" class="form-label">توضیحات متا انگلیسی</label>
                                            <textarea class="form-control" id="meta_description_en" name="meta_description_en" rows="3">{{ old('meta_description_en') }}</textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="card">
                            <div class="card-body">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-save me-1"></i>ذخیره دسته‌بندی
                                </button>
                                <a href="{{ route('admin.categories.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>انصراف
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <div class="col-lg-4">
                    <!-- Help -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">راهنما</h5>
                        </div>
                        <div class="card-body">
                            <h6>نوع دسته‌بندی:</h6>
                            <ul class="small">
                                <li><strong>مقاله:</strong> فقط برای مقالات استفاده می‌شود</li>
                                <li><strong>خبر:</strong> فقط برای اخبار استفاده می‌شود</li>
                                <li><strong>هر دو:</strong> برای مقالات و اخبار استفاده می‌شود</li>
                            </ul>

                            <h6 class="mt-3">دسته‌بندی والد:</h6>
                            <p class="small">اگر این دسته‌بندی زیرمجموعه دسته‌بندی دیگری است، والد آن را انتخاب کنید.</p>

                            <h6 class="mt-3">ترتیب نمایش:</h6>
                            <p class="small">عدد کمتر، اولویت بالاتر در نمایش دارد.</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}
.sidebar .nav-link {
    font-weight: 500;
    color: #333;
}
.sidebar .nav-link.active {
    color: #007bff;
}
</style>
@endsection
