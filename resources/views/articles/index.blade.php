@extends('layouts.welcome')

@section('title', app()->getLocale() == 'fa' ? 'مقالات' : 'Articles')

@section('meta_description', app()->getLocale() == 'fa' ? 'مطالعه آخرین مقالات تخصصی در زمینه حسابداری، مالی و تجارت' : 'Read the latest professional articles on accounting, finance and business')

@section('content')
<div class="container py-5">
    <!-- Header Section -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold text-primary mb-3">
                {{ app()->getLocale() == 'fa' ? 'مقالات تخصصی' : 'Professional Articles' }}
            </h1>
            <p class="lead text-muted">
                {{ app()->getLocale() == 'fa' ? 'آخرین مقالات و تحلیل‌های تخصصی در زمینه حسابداری، مالی و تجارت' : 'Latest articles and professional analysis in accounting, finance and business' }}
            </p>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 mb-4">
            <!-- Search -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">{{ app()->getLocale() == 'fa' ? 'جستجو' : 'Search' }}</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('articles.index') }}">
                        <div class="input-group">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="{{ app()->getLocale() == 'fa' ? 'جستجو در مقالات...' : 'Search articles...' }}" 
                                   value="{{ request('search') }}">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Categories -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ app()->getLocale() == 'fa' ? 'دسته‌بندی‌ها' : 'Categories' }}</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="{{ route('articles.index') }}" 
                               class="text-decoration-none {{ !request('category') ? 'fw-bold text-primary' : 'text-dark' }}">
                                {{ app()->getLocale() == 'fa' ? 'همه مقالات' : 'All Articles' }}
                            </a>
                        </li>
                        @foreach($categories as $category)
                            <li class="mb-2">
                                <a href="{{ route('articles.category', $category->slug) }}" 
                                   class="text-decoration-none text-dark">
                                    {{ $category->name }}
                                </a>
                                @if($category->children->count() > 0)
                                    <ul class="list-unstyled ms-3 mt-1">
                                        @foreach($category->children as $child)
                                            <li class="mb-1">
                                                <a href="{{ route('articles.category', $child->slug) }}" 
                                                   class="text-decoration-none text-muted small">
                                                    {{ $child->name }}
                                                </a>
                                            </li>
                                        @endforeach
                                    </ul>
                                @endif
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>

        <!-- Articles Grid -->
        <div class="col-lg-9">
            @if($articles->count() > 0)
                <div class="row">
                    @foreach($articles as $article)
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 shadow-sm">
                                @if($article->featured_image)
                                    <img src="{{ asset('storage/' . $article->featured_image) }}" 
                                         class="card-img-top" 
                                         alt="{{ $article->title }}"
                                         style="height: 200px; object-fit: cover;">
                                @else
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                         style="height: 200px;">
                                        <i class="fas fa-file-alt fa-3x text-muted"></i>
                                    </div>
                                @endif
                                
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title">
                                        <a href="{{ route('articles.show', $article->slug) }}" 
                                           class="text-decoration-none text-dark">
                                            {{ $article->title }}
                                        </a>
                                    </h5>
                                    
                                    <p class="card-text text-muted small mb-2">
                                        {{ Str::limit($article->summary ?? strip_tags($article->content), 100) }}
                                    </p>
                                    
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i>{{ $article->author->name }}
                                            </small>
                                            <small class="text-muted">
                                                <i class="fas fa-eye me-1"></i>{{ $article->views_count }}
                                            </small>
                                        </div>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                {{ $article->published_at->format('M d, Y') }}
                                            </small>
                                            <a href="{{ route('articles.show', $article->slug) }}" 
                                               class="btn btn-primary btn-sm">
                                                {{ app()->getLocale() == 'fa' ? 'ادامه مطلب' : 'Read More' }}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $articles->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">
                        {{ app()->getLocale() == 'fa' ? 'مقاله‌ای یافت نشد' : 'No articles found' }}
                    </h4>
                    <p class="text-muted">
                        {{ app()->getLocale() == 'fa' ? 'لطفاً عبارت جستجوی دیگری امتحان کنید' : 'Please try a different search term' }}
                    </p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
