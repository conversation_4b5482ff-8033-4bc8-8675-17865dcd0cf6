<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'fa' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - {{ app()->getLocale() == 'fa' ? 'پنل مدیریت' : 'Admin Panel' }}</title>

    <!-- DNS prefetch -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">

    <!-- Fonts -->
    <link href="https://fonts.bunny.net/css?family=Nunito" rel="stylesheet">

    <!-- Persian Font -->
    @if(app()->getLocale() == 'fa')
        <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazir-font@v30.1.0/dist/font-face.css" rel="stylesheet" type="text/css" />
    @endif

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Custom Styles -->
    @vite(['resources/css/app.css'])

    <!-- RTL Fixes -->
    @if(app()->getLocale() == 'fa')
        <link href="{{ asset('css/rtl-fixes.css') }}" rel="stylesheet">
    @endif

    <style>
        body {
            overflow-x: hidden;
        }

        /* Sidebar Styles */
        .admin-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #63D1CC 100%);
            z-index: 1000;
            overflow-y: auto;
            overflow-x: hidden;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 2px 0 10px rgba(0,0,0,0.15);
        }

        .admin-sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .admin-sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .admin-sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .admin-sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        html[dir="rtl"] .admin-sidebar {
            left: auto;
            right: 0;
        }

        .admin-sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin-bottom: 0.25rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .admin-sidebar .nav-link:hover,
        .admin-sidebar .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        html[dir="rtl"] .admin-sidebar .nav-link:hover,
        html[dir="rtl"] .admin-sidebar .nav-link.active {
            transform: translateX(-5px);
        }

        .admin-sidebar .nav-link i {
            width: 20px;
            text-align: center;
            margin-right: 0.5rem;
            flex-shrink: 0;
        }

        html[dir="rtl"] .admin-sidebar .nav-link i {
            margin-right: 0;
            margin-left: 0.5rem;
        }

        /* Main Content Styles */
        .admin-content {
            margin-left: 250px;
            background-color: #f8f9fa;
            min-height: 100vh;
            padding: 0;
        }

        html[dir="rtl"] .admin-content {
            margin-left: 0;
            margin-right: 250px;
        }

        .admin-header {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 1.5rem;
            margin-bottom: 0;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .admin-main-content {
            padding: 2rem 1.5rem;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
            }

            html[dir="rtl"] .admin-sidebar {
                transform: translateX(100%);
            }

            .admin-sidebar.show {
                transform: translateX(0);
            }

            .admin-content {
                margin-left: 0;
            }

            html[dir="rtl"] .admin-content {
                margin-right: 0;
            }
        }

        /* Mobile Toggle Button */
        .sidebar-toggle {
            display: none;
        }

        @media (max-width: 768px) {
            .sidebar-toggle {
                display: inline-block;
            }
        }

        /* Overlay for mobile */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        @media (max-width: 768px) {
            .sidebar-overlay.show {
                display: block;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- Sidebar Overlay for Mobile -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Sidebar -->
        <nav class="admin-sidebar" id="adminSidebar">
            <div class="pt-3 px-3">
                <!-- Logo -->
                <div class="text-center mb-4">
                    <a href="{{ route('admin_home') }}" class="text-decoration-none">
                        <img src="{{ asset('images/logo.png') }}" alt="Lian Taraz" height="50" class="mb-2">
                        <h5 class="text-white">{{ app()->getLocale() == 'fa' ? 'پنل مدیریت' : 'Admin Panel' }}</h5>
                    </a>
                </div>

                <!-- Navigation -->
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" href="{{ route('admin.dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>{{ app()->getLocale() == 'fa' ? 'داشبورد' : 'Dashboard' }}</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.categories.*') ? 'active' : '' }}" href="{{ route('admin.categories.index') }}">
                            <i class="fas fa-tags"></i>
                            <span>{{ app()->getLocale() == 'fa' ? 'دسته‌بندی‌ها' : 'Categories' }}</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.articles.*') ? 'active' : '' }}" href="{{ route('admin.articles.index') }}">
                            <i class="fas fa-file-alt"></i>
                            <span>{{ app()->getLocale() == 'fa' ? 'مقالات' : 'Articles' }}</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('admin.news.*') ? 'active' : '' }}" href="{{ route('admin.news.index') }}">
                            <i class="fas fa-newspaper"></i>
                            <span>{{ app()->getLocale() == 'fa' ? 'اخبار' : 'News' }}</span>
                        </a>
                    </li>

                    <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">

                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('home') }}" target="_blank">
                            <i class="fas fa-external-link-alt"></i>
                            <span>{{ app()->getLocale() == 'fa' ? 'مشاهده سایت' : 'View Site' }}</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('logout') }}"
                           onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>{{ app()->getLocale() == 'fa' ? 'خروج' : 'Logout' }}</span>
                        </a>
                        <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                            @csrf
                        </form>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="admin-content">
            <!-- Header -->
            <div class="admin-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <!-- Mobile Sidebar Toggle -->
                        <button class="btn btn-outline-secondary sidebar-toggle me-3" type="button" id="sidebarToggle">
                            <i class="fas fa-bars"></i>
                        </button>
                        <div>
                            <h4 class="mb-0">@yield('page-title', app()->getLocale() == 'fa' ? 'پنل مدیریت' : 'Admin Panel')</h4>
                            <small class="text-muted">@yield('page-subtitle', '')</small>
                        </div>
                    </div>
                    <div>
                        <span class="text-muted">{{ app()->getLocale() == 'fa' ? 'خوش آمدید' : 'Welcome' }}, {{ Auth::user()->name }}</span>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="admin-main-content">
                @yield('content')
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom Scripts -->
    @vite(['resources/js/app.js'])

    <!-- CKEditor 5 -->
    <script src="https://cdn.ckeditor.com/ckeditor5/40.0.0/classic/ckeditor.js"></script>

    <!-- CKEditor Custom Styles -->
    <style>
        .ck-editor__editable {
            min-height: 300px;
        }
        .ck-editor__editable_inline {
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
        }
        .ck-editor__main {
            direction: rtl;
        }
        .ck-content {
            font-family: 'Vazirmatn', sans-serif;
            font-size: 14px;
            line-height: 1.6;
        }
        .ck-content h1, .ck-content h2, .ck-content h3, .ck-content h4 {
            font-family: 'Vazirmatn', sans-serif;
            font-weight: bold;
        }
    </style>

    <!-- Admin Layout Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('adminSidebar');
            const overlay = document.getElementById('sidebarOverlay');

            // Toggle sidebar on mobile
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                    overlay.classList.toggle('show');
                });
            }

            // Close sidebar when clicking overlay
            if (overlay) {
                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                    overlay.classList.remove('show');
                });
            }

            // Close sidebar when clicking on a link (mobile)
            const sidebarLinks = sidebar.querySelectorAll('.nav-link');
            sidebarLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        sidebar.classList.remove('show');
                        overlay.classList.remove('show');
                    }
                });
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    sidebar.classList.remove('show');
                    overlay.classList.remove('show');
                }
            });
        });
    </script>

    @yield('scripts')
</body>
</html>
