@extends('layouts.welcome')

@section('title', app()->getLocale() == 'fa' ? 'اخبار' : 'News')

@section('meta_description', app()->getLocale() == 'fa' ? 'آخرین اخبار و رویدادهای مهم در زمینه حسابداری، مالی و تجارت' : 'Latest news and important events in accounting, finance and business')

@section('content')
<div class="container py-5">
    <!-- Header Section -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold text-primary mb-3">
                {{ app()->getLocale() == 'fa' ? 'آخرین اخبار' : 'Latest News' }}
            </h1>
            <p class="lead text-muted">
                {{ app()->getLocale() == 'fa' ? 'آخرین اخبار و رویدادهای مهم در زمینه حسابداری، مالی و تجارت' : 'Latest news and important events in accounting, finance and business' }}
            </p>
        </div>
    </div>

    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 mb-4">
            <!-- Search -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">{{ app()->getLocale() == 'fa' ? 'جستجو' : 'Search' }}</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('news.index') }}">
                        <div class="input-group">
                            <input type="text" class="form-control" name="search"
                                   placeholder="{{ app()->getLocale() == 'fa' ? 'جستجو در اخبار...' : 'Search news...' }}"
                                   value="{{ request('search') }}">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Categories -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ app()->getLocale() == 'fa' ? 'دسته‌بندی‌ها' : 'Categories' }}</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <a href="{{ route('news.index') }}"
                               class="text-decoration-none {{ !request('category') ? 'fw-bold text-primary' : 'text-dark' }}">
                                {{ app()->getLocale() == 'fa' ? 'همه اخبار' : 'All News' }}
                            </a>
                        </li>
                        @foreach($categories as $category)
                            <li class="mb-2">
                                <a href="{{ route('news.category', $category->slug) }}"
                                   class="text-decoration-none text-dark">
                                    {{ $category->name }}
                                </a>
                                @if($category->children->count() > 0)
                                    <ul class="list-unstyled ms-3 mt-1">
                                        @foreach($category->children as $child)
                                            <li class="mb-1">
                                                <a href="{{ route('news.category', $child->slug) }}"
                                                   class="text-decoration-none text-muted small">
                                                    {{ $child->name }}
                                                </a>
                                            </li>
                                        @endforeach
                                    </ul>
                                @endif
                            </li>
                        @endforeach
                    </ul>
                </div>
            </div>
        </div>

        <!-- News Grid -->
        <div class="col-lg-9">
            @if($news->count() > 0)
                <div class="row">
                    @foreach($news as $newsItem)
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 shadow-sm">
                                @if($newsItem->featured_image)
                                    <img src="{{ asset('storage/' . $newsItem->featured_image) }}"
                                         class="card-img-top"
                                         alt="{{ $newsItem->title }}"
                                         style="height: 200px; object-fit: cover;">
                                @else
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                         style="height: 200px;">
                                        <i class="fas fa-newspaper fa-3x text-muted"></i>
                                    </div>
                                @endif

                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title">
                                        <a href="{{ route('news.show', $newsItem->slug) }}"
                                           class="text-decoration-none text-dark">
                                            {{ $newsItem->title }}
                                        </a>
                                    </h5>

                                    <p class="card-text text-muted small mb-2">
                                        {{ Str::limit($newsItem->summary ?? strip_tags($newsItem->content), 100) }}
                                    </p>

                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i>{{ $newsItem->author->name }}
                                            </small>
                                            <small class="text-muted">
                                                <i class="fas fa-eye me-1"></i>{{ $newsItem->views_count }}
                                            </small>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                {{ app()->getLocale() == 'fa' ? $newsItem->persian_published_at : $newsItem->published_at->format('M d, Y') }}
                                            </small>
                                            <a href="{{ route('news.show', $newsItem->slug) }}"
                                               class="btn btn-primary btn-sm">
                                                {{ app()->getLocale() == 'fa' ? 'ادامه مطلب' : 'Read More' }}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $news->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">
                        {{ app()->getLocale() == 'fa' ? 'خبری یافت نشد' : 'No news found' }}
                    </h4>
                    <p class="text-muted">
                        {{ app()->getLocale() == 'fa' ? 'لطفاً عبارت جستجوی دیگری امتحان کنید' : 'Please try a different search term' }}
                    </p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
