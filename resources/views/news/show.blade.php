@extends('layouts.welcome')

@section('title', $news->meta_title ?? $news->title)

@section('meta_description', $news->meta_description ?? Str::limit(strip_tags($news->content), 160))

@section('meta_keywords', $news->meta_keywords ? implode(', ', $news->meta_keywords) : '')

@section('content')
<div class="container py-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ route('home') }}" class="text-decoration-none">
                    {{ app()->getLocale() == 'fa' ? 'خانه' : 'Home' }}
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ route('news.index') }}" class="text-decoration-none">
                    {{ app()->getLocale() == 'fa' ? 'اخبار' : 'News' }}
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">{{ $news->title }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- News Content -->
        <div class="col-lg-8">
            <article class="mb-5">
                <!-- News Header -->
                <header class="mb-4">
                    <h1 class="display-5 fw-bold text-primary mb-3">{{ $news->title }}</h1>

                    <!-- News Meta -->
                    <div class="d-flex flex-wrap align-items-center text-muted mb-3">
                        <span class="me-4">
                            <i class="fas fa-user me-2"></i>{{ $news->author->name }}
                        </span>
                        <span class="me-4">
                            <i class="fas fa-calendar me-2"></i>{{ app()->getLocale() == 'fa' ? $news->persian_published_at : $news->published_at->format('M d, Y') }}
                        </span>
                        <span class="me-4">
                            <i class="fas fa-eye me-2"></i>{{ $news->views_count }} {{ app()->getLocale() == 'fa' ? 'بازدید' : 'views' }}
                        </span>
                        <span>
                            <i class="fas fa-clock me-2"></i>{{ $news->reading_time }} {{ app()->getLocale() == 'fa' ? 'دقیقه مطالعه' : 'min read' }}
                        </span>
                    </div>

                    <!-- Categories -->
                    @if($news->categories->count() > 0)
                        <div class="mb-3">
                            @foreach($news->categories as $category)
                                <a href="{{ route('news.category', $category->slug) }}"
                                   class="badge bg-primary text-decoration-none me-2">
                                    {{ $category->name }}
                                </a>
                            @endforeach
                        </div>
                    @endif

                    <!-- Featured Image -->
                    @if($news->featured_image)
                        <div class="mb-4">
                            <img src="{{ asset('storage/' . $news->featured_image) }}"
                                 class="img-fluid rounded shadow"
                                 alt="{{ $news->title }}"
                                 style="width: 100%; max-height: 400px; object-fit: cover;">
                        </div>
                    @endif
                </header>

                <!-- News Summary -->
                @if($news->summary)
                    <div class="alert alert-light border-start border-primary border-4 mb-4">
                        <h5 class="fw-bold mb-2">{{ app()->getLocale() == 'fa' ? 'خلاصه خبر' : 'News Summary' }}</h5>
                        <p class="mb-0">{{ $news->summary }}</p>
                    </div>
                @endif

                <!-- News Content -->
                <div class="news-content">
                    {!! $news->content !!}
                </div>

                <!-- News Gallery -->
                @if($news->gallery && count($news->gallery) > 0)
                    <div class="mt-5">
                        <h4 class="fw-bold mb-3">{{ app()->getLocale() == 'fa' ? 'گالری تصاویر' : 'Image Gallery' }}</h4>
                        <div class="row">
                            @foreach($news->gallery as $image)
                                <div class="col-md-4 mb-3">
                                    <img src="{{ asset('storage/' . $image) }}"
                                         class="img-fluid rounded shadow-sm"
                                         alt="Gallery Image"
                                         data-bs-toggle="modal"
                                         data-bs-target="#imageModal"
                                         style="cursor: pointer; height: 200px; width: 100%; object-fit: cover;">
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif

                <!-- Share Buttons -->
                <div class="mt-5 pt-4 border-top">
                    <h5 class="fw-bold mb-3">{{ app()->getLocale() == 'fa' ? 'اشتراک‌گذاری' : 'Share News' }}</h5>
                    <div class="d-flex gap-2">
                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->fullUrl()) }}"
                           target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-facebook-f me-1"></i>Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->fullUrl()) }}&text={{ urlencode($news->title) }}"
                           target="_blank" class="btn btn-outline-info btn-sm">
                            <i class="fab fa-twitter me-1"></i>Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode(request()->fullUrl()) }}"
                           target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-linkedin-in me-1"></i>LinkedIn
                        </a>
                        <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard()">
                            <i class="fas fa-link me-1"></i>{{ app()->getLocale() == 'fa' ? 'کپی لینک' : 'Copy Link' }}
                        </button>
                    </div>
                </div>
            </article>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Related News -->
            @if($relatedNews->count() > 0)
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ app()->getLocale() == 'fa' ? 'اخبار مرتبط' : 'Related News' }}</h5>
                    </div>
                    <div class="card-body">
                        @foreach($relatedNews as $related)
                            <div class="d-flex mb-3 {{ !$loop->last ? 'border-bottom pb-3' : '' }}">
                                @if($related->featured_image)
                                    <img src="{{ asset('storage/' . $related->featured_image) }}"
                                         class="me-3 rounded"
                                         alt="{{ $related->title }}"
                                         style="width: 80px; height: 60px; object-fit: cover;">
                                @else
                                    <div class="me-3 bg-light rounded d-flex align-items-center justify-content-center"
                                         style="width: 80px; height: 60px;">
                                        <i class="fas fa-newspaper text-muted"></i>
                                    </div>
                                @endif
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="{{ route('news.show', $related->slug) }}"
                                           class="text-decoration-none text-dark">
                                            {{ Str::limit($related->title, 60) }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        {{ $related->published_at->format('M d, Y') }}
                                    </small>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Back to News -->
            <div class="card">
                <div class="card-body text-center">
                    <a href="{{ route('news.index') }}" class="btn btn-primary">
                        <i class="fas fa-arrow-left me-2"></i>
                        {{ app()->getLocale() == 'fa' ? 'بازگشت به اخبار' : 'Back to News' }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body p-0">
                <img src="" class="img-fluid w-100" alt="Gallery Image" id="modalImage">
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        alert('{{ app()->getLocale() == 'fa' ? 'لینک کپی شد!' : 'Link copied!' }}');
    });
}

// Image modal functionality
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('[data-bs-target="#imageModal"]');
    const modalImage = document.getElementById('modalImage');

    images.forEach(img => {
        img.addEventListener('click', function() {
            modalImage.src = this.src;
        });
    });
});
</script>
@endsection
