@extends('layouts.welcome')

@section('title', $category->meta_title ?? $category->name . ' - ' . (app()->getLocale() == 'fa' ? 'اخبار' : 'News'))

@section('meta_description', $category->meta_description ?? $category->description)

@section('content')
<div class="container py-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ route('home') }}" class="text-decoration-none">
                    {{ app()->getLocale() == 'fa' ? 'خانه' : 'Home' }}
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ route('news.index') }}" class="text-decoration-none">
                    {{ app()->getLocale() == 'fa' ? 'اخبار' : 'News' }}
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                {{ app()->getLocale() == 'fa' ? $category->name_fa : ($category->name_en ?? $category->name_fa) }}
            </li>
        </ol>
    </nav>

    <!-- Category Header -->
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h1 class="display-4 fw-bold text-primary mb-3">
                {{ app()->getLocale() == 'fa' ? $category->name_fa : ($category->name_en ?? $category->name_fa) }}
            </h1>
            @php
                $description = app()->getLocale() == 'fa' ? $category->description_fa : ($category->description_en ?? $category->description_fa);
            @endphp
            @if($description)
                <p class="lead text-muted">{{ $description }}</p>
            @endif
            <div class="text-muted">
                <i class="fas fa-folder me-2"></i>
                {{ $news->total() }} {{ app()->getLocale() == 'fa' ? 'خبر' : 'news' }}
            </div>
        </div>
    </div>

    @if($news->count() > 0)
        <div class="row">
            @foreach($news as $newsItem)
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        @if($newsItem->featured_image)
                            <img src="{{ asset('storage/' . $newsItem->featured_image) }}"
                                 class="card-img-top"
                                 alt="{{ $newsItem->title }}"
                                 style="height: 200px; object-fit: cover;">
                        @else
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                 style="height: 200px;">
                                <i class="fas fa-newspaper fa-3x text-muted"></i>
                            </div>
                        @endif

                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">
                                <a href="{{ route('news.show', $newsItem->slug) }}"
                                   class="text-decoration-none text-dark">
                                    {{ app()->getLocale() == 'fa' ? $newsItem->title_fa : ($newsItem->title_en ?? $newsItem->title_fa) }}
                                </a>
                            </h5>

                            <p class="card-text text-muted small mb-2">
                                @php
                                    $summary = app()->getLocale() == 'fa' ? $newsItem->summary_fa : ($newsItem->summary_en ?? $newsItem->summary_fa);
                                    $content = app()->getLocale() == 'fa' ? $newsItem->content_fa : ($newsItem->content_en ?? $newsItem->content_fa);
                                    $excerpt = $summary ?? strip_tags($content);
                                @endphp
                                {{ Str::limit($excerpt, 100) }}
                            </p>

                            <!-- Categories -->
                            @if($newsItem->categories->count() > 0)
                                <div class="mb-2">
                                    @foreach($newsItem->categories->take(2) as $cat)
                                        <span class="badge bg-light text-dark me-1">
                                            {{ app()->getLocale() == 'fa' ? $cat->name_fa : ($cat->name_en ?? $cat->name_fa) }}
                                        </span>
                                    @endforeach
                                </div>
                            @endif

                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>{{ $newsItem->author->name }}
                                    </small>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>{{ $newsItem->views_count }}
                                    </small>
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        {{ app()->getLocale() == 'fa' ? $newsItem->persian_published_at : $newsItem->published_at->format('M d, Y') }}
                                    </small>
                                    <a href="{{ route('news.show', $newsItem->slug) }}"
                                       class="btn btn-primary btn-sm">
                                        {{ app()->getLocale() == 'fa' ? 'ادامه مطلب' : 'Read More' }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="d-flex justify-content-center mt-4">
            {{ $news->links() }}
        </div>
    @else
        <div class="text-center py-5">
            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">
                {{ app()->getLocale() == 'fa' ? 'خبری در این دسته‌بندی یافت نشد' : 'No news found in this category' }}
            </h4>
            <p class="text-muted">
                {{ app()->getLocale() == 'fa' ? 'به زودی اخبار جدیدی اضافه خواهد شد' : 'New news will be added soon' }}
            </p>
            <a href="{{ route('news.index') }}" class="btn btn-primary mt-3">
                {{ app()->getLocale() == 'fa' ? 'مشاهده همه اخبار' : 'View All News' }}
            </a>
        </div>
    @endif

    <!-- Related Categories -->
    @php
        $relatedCategories = \App\Models\Category::active()
            ->forType('news')
            ->where('id', '!=', $category->id)
            ->parents()
            ->ordered()
            ->limit(6)
            ->get();
    @endphp

    @if($relatedCategories->count() > 0)
        <div class="mt-5 pt-5 border-top">
            <h3 class="text-center mb-4">{{ app()->getLocale() == 'fa' ? 'دسته‌بندی‌های مرتبط' : 'Related Categories' }}</h3>
            <div class="row">
                @foreach($relatedCategories as $relatedCategory)
                    <div class="col-md-4 col-lg-2 mb-3">
                        <a href="{{ route('news.category', $relatedCategory->slug) }}"
                           class="btn btn-outline-primary w-100 text-center">
                            {{ app()->getLocale() == 'fa' ? $relatedCategory->name_fa : ($relatedCategory->name_en ?? $relatedCategory->name_fa) }}
                        </a>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
</div>
@endsection
