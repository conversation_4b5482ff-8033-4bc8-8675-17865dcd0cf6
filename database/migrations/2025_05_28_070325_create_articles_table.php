<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('articles', function (Blueprint $table) {
            $table->id();
            $table->string('title_fa'); // عنوان فارسی
            $table->string('title_en')->nullable(); // عنوان انگلیسی
            $table->string('slug')->unique(); // برای URL
            $table->text('summary_fa')->nullable(); // خلاصه فارسی
            $table->text('summary_en')->nullable(); // خلاصه انگلیسی
            $table->longText('content_fa'); // محتوای فارسی
            $table->longText('content_en')->nullable(); // محتوای انگلیسی
            $table->string('featured_image')->nullable(); // تصویر شاخص
            $table->json('gallery')->nullable(); // گالری تصاویر
            $table->boolean('is_published')->default(false); // منتشر شده
            $table->boolean('is_featured')->default(false); // ویژه (برای اسلایدشو)
            $table->timestamp('published_at')->nullable(); // تاریخ انتشار
            $table->unsignedBigInteger('author_id'); // نویسنده
            $table->unsignedInteger('views_count')->default(0); // تعداد بازدید
            $table->string('meta_title_fa')->nullable(); // SEO
            $table->string('meta_title_en')->nullable(); // SEO
            $table->text('meta_description_fa')->nullable(); // SEO
            $table->text('meta_description_en')->nullable(); // SEO
            $table->json('meta_keywords')->nullable(); // کلمات کلیدی
            $table->integer('sort_order')->default(0); // ترتیب نمایش
            $table->timestamps();

            $table->foreign('author_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['is_published', 'published_at']);
            $table->index(['is_featured', 'is_published']);
            $table->index('views_count');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('articles');
    }
};
