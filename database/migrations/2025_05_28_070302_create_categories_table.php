<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name_fa'); // نام فارسی
            $table->string('name_en')->nullable(); // نام انگلیسی
            $table->string('slug')->unique(); // برای URL
            $table->text('description_fa')->nullable(); // توضیحات فارسی
            $table->text('description_en')->nullable(); // توضیحات انگلیسی
            $table->string('type')->default('both'); // article, news, both
            $table->unsignedBigInteger('parent_id')->nullable(); // برای دسته‌بندی تودرتو
            $table->integer('sort_order')->default(0); // ترتیب نمایش
            $table->boolean('is_active')->default(true); // فعال/غیرفعال
            $table->string('meta_title_fa')->nullable(); // SEO
            $table->string('meta_title_en')->nullable(); // SEO
            $table->text('meta_description_fa')->nullable(); // SEO
            $table->text('meta_description_en')->nullable(); // SEO
            $table->timestamps();

            $table->foreign('parent_id')->references('id')->on('categories')->onDelete('set null');
            $table->index(['type', 'is_active']);
            $table->index(['parent_id', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
