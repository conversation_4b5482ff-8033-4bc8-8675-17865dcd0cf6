<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // دسته‌بندی‌های اصلی مقالات
        $articleCategories = [
            [
                'name_fa' => 'حسابداری',
                'name_en' => 'Accounting',
                'slug' => 'accounting',
                'description_fa' => 'مقالات مربوط به حسابداری و امور مالی',
                'description_en' => 'Articles related to accounting and financial affairs',
                'type' => 'article',
                'sort_order' => 1,
                'children' => [
                    [
                        'name_fa' => 'حسابداری مالی',
                        'name_en' => 'Financial Accounting',
                        'slug' => 'financial-accounting',
                        'type' => 'article',
                        'sort_order' => 1,
                    ],
                    [
                        'name_fa' => 'حسابداری مدیریت',
                        'name_en' => 'Management Accounting',
                        'slug' => 'management-accounting',
                        'type' => 'article',
                        'sort_order' => 2,
                    ]
                ]
            ],
            [
                'name_fa' => 'مالیات',
                'name_en' => 'Tax',
                'slug' => 'tax',
                'description_fa' => 'مقالات مربوط به قوانین مالیاتی',
                'description_en' => 'Articles related to tax laws',
                'type' => 'article',
                'sort_order' => 2,
                'children' => [
                    [
                        'name_fa' => 'مالیات بر درآمد',
                        'name_en' => 'Income Tax',
                        'slug' => 'income-tax',
                        'type' => 'article',
                        'sort_order' => 1,
                    ],
                    [
                        'name_fa' => 'مالیات بر ارزش افزوده',
                        'name_en' => 'VAT',
                        'slug' => 'vat',
                        'type' => 'article',
                        'sort_order' => 2,
                    ]
                ]
            ],
            [
                'name_fa' => 'تجارت',
                'name_en' => 'Business',
                'slug' => 'business',
                'description_fa' => 'مقالات مربوط به تجارت و کسب و کار',
                'description_en' => 'Articles related to business and commerce',
                'type' => 'article',
                'sort_order' => 3,
            ]
        ];

        // دسته‌بندی‌های اصلی اخبار
        $newsCategories = [
            [
                'name_fa' => 'اخبار اقتصادی',
                'name_en' => 'Economic News',
                'slug' => 'economic-news',
                'description_fa' => 'آخرین اخبار اقتصادی کشور و جهان',
                'description_en' => 'Latest economic news from country and world',
                'type' => 'news',
                'sort_order' => 1,
            ],
            [
                'name_fa' => 'اخبار مالیاتی',
                'name_en' => 'Tax News',
                'slug' => 'tax-news',
                'description_fa' => 'آخرین اخبار و تغییرات قوانین مالیاتی',
                'description_en' => 'Latest tax news and law changes',
                'type' => 'news',
                'sort_order' => 2,
            ],
            [
                'name_fa' => 'اخبار بازار',
                'name_en' => 'Market News',
                'slug' => 'market-news',
                'description_fa' => 'اخبار بازار سرمایه و بورس',
                'description_en' => 'Capital market and stock exchange news',
                'type' => 'news',
                'sort_order' => 3,
            ]
        ];

        // ایجاد دسته‌بندی‌های مقالات
        foreach ($articleCategories as $categoryData) {
            $children = $categoryData['children'] ?? [];
            unset($categoryData['children']);

            $category = Category::create($categoryData);

            // ایجاد زیردسته‌ها
            foreach ($children as $childData) {
                $childData['parent_id'] = $category->id;
                Category::create($childData);
            }
        }

        // ایجاد دسته‌بندی‌های اخبار
        foreach ($newsCategories as $categoryData) {
            Category::create($categoryData);
        }

        // دسته‌بندی مشترک
        Category::create([
            'name_fa' => 'عمومی',
            'name_en' => 'General',
            'slug' => 'general',
            'description_fa' => 'مطالب عمومی',
            'description_en' => 'General content',
            'type' => 'both',
            'sort_order' => 99,
        ]);
    }
}
